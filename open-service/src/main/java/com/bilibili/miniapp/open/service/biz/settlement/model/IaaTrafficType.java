package com.bilibili.miniapp.open.service.biz.settlement.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Getter
@Deprecated(since = "仅在小游戏结算中会使用到")
@RequiredArgsConstructor
public enum IaaTrafficType {

    unknown(0, "未知"),

    natural(1, "自然流量"),

    business(2, "商业流量"),

    ;


    private final int code;

    private final String desc;


}
