package com.bilibili.miniapp.open.service.biz.income.impl;

import com.bilibili.miniapp.open.common.exception.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenIaaIncomeDetailDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryRespBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppListBo;
import com.bilibili.miniapp.open.service.common.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收入服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class IncomeService implements IIncomeService {

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private IMiniAppService miniAppService;

    @Autowired
    private MiniAppOpenIaaIncomeDetailDao iaaIncomeDetailDao;

    /**
     * 提现状态枚举
     */
    private static final int WITHDRAW_STATUS_FROZEN = 0;      // 提现冻结
    private static final int WITHDRAW_STATUS_WITHDRAWABLE = 1; // 可提现
    private static final int WITHDRAW_STATUS_WITHDRAWING = 2;  // 提现中
    private static final int WITHDRAW_STATUS_SUCCESS = 3;      // 提现成功

    @Override
    public IncomeSummaryRespBo getIncomeSummary(Long mid) {
        // 1. 获取企业信息
        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);
        AssertUtil.isTrue(company != null, ErrorCodeType.NO_DATA.getCode(), "未找到企业信息");

        // 2. 获取用户的小程序列表
        List<String> appIds = getUserAppIds(mid);
        if (CollectionUtils.isEmpty(appIds)) {
            log.info("[IncomeService] 用户没有小程序, mid={}", mid);
            return IncomeSummaryRespBo.builder()
                    .withdrawableAmount(BigDecimal.ZERO)
                    .withdrawingAmount(BigDecimal.ZERO)
                    .build();
        }

        // 3. 查询收入明细并汇总
        return calculateIncomeSummary(appIds);
    }

    /**
     * 获取用户的小程序ID列表
     */
    private List<String> getUserAppIds(Long mid) {
        try {
            // 查询第一页，获取所有小程序
            PageResult<MiniAppListBo> pageResult = miniAppService.queryMiniAppList(mid, null, 1, 100);
            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                return List.of();
            }
            
            return pageResult.getList().stream()
                    .map(MiniAppListBo::getAppId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[IncomeService] 获取用户小程序列表失败, mid={}", mid, e);
            return List.of();
        }
    }

    /**
     * 计算收入汇总
     */
    private IncomeSummaryRespBo calculateIncomeSummary(List<String> appIds) {
        // 查询可提现金额
        BigDecimal withdrawableAmount = calculateAmountByStatus(appIds, WITHDRAW_STATUS_WITHDRAWABLE);
        
        // 查询提现中金额
        BigDecimal withdrawingAmount = calculateAmountByStatus(appIds, WITHDRAW_STATUS_WITHDRAWING);

        return IncomeSummaryRespBo.builder()
                .withdrawableAmount(withdrawableAmount)
                .withdrawingAmount(withdrawingAmount)
                .build();
    }

    /**
     * 根据状态计算金额
     */
    private BigDecimal calculateAmountByStatus(List<String> appIds, int withdrawStatus) {
        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        example.createCriteria()
                .andAppIdIn(appIds)
                .andWithdrawStatusEqualTo(withdrawStatus)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);
        
        if (CollectionUtils.isEmpty(incomeDetails)) {
            return BigDecimal.ZERO;
        }

        return incomeDetails.stream()
                .map(MiniAppOpenIaaIncomeDetailPo::getActualIncomeAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
