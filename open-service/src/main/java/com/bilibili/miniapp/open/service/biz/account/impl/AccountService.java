package com.bilibili.miniapp.open.service.biz.account.impl;

import com.bapis.account.service.Info;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppQuery;
import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.UserInfoServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppMemberRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Slf4j
@Service
public class AccountService implements IAccountService {

    @Autowired
    private UserInfoServiceGrpcClient userInfoServiceGrpcClient;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Override
    public AccountInfoBo getAccountInfo(long mid) {
        Map<Long, Info> longInfoMap = userInfoServiceGrpcClient.infoMap(List.of(mid));
        AccountInfoBo accountInfoBo = new AccountInfoBo();
        if (longInfoMap.containsKey(mid)) {
            Info info = longInfoMap.get(mid);
            accountInfoBo.setFace(info.getFace());
            accountInfoBo.setNickName(info.getName());
            accountInfoBo.setMid(mid);
        }
        return accountInfoBo;
    }


    @Override
    public List<AccountInfoBo> getAccountInfos(List<Long> midList) {
        List<AccountInfoBo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(midList)) {
            return result;
        }
        Map<Long, Info> map = userInfoServiceGrpcClient.infoMap(midList);

        map.forEach((mid, info) -> {
            AccountInfoBo accountInfoBo = new AccountInfoBo();
            accountInfoBo.setFace(info.getFace());
            accountInfoBo.setNickName(info.getName());
            accountInfoBo.setMid(mid);
            result.add(accountInfoBo);
        });
        return result;
    }

    /**
     * 仅判断超管的情况下，只需要查一个外部小程序列表接口,否则需要额外调用MiniAppMemberRemoteService，暂时业务上也只需要判断是不是超管
     * 等后续拓展时增加其他权限查询
     */
    @Override
    public Integer getUserRole(Long mid, String appId) {
        if (isAdmin(mid, appId)) {
            return MiniAppPermission.SUPER_ADMIN.getCode();
        }
        return null;
    }

    @Override
    public boolean isAdmin(Long mid, String appId, CompanyDetailBo companyDetail) {
        if (StringUtils.isBlank(appId)
                && companyDetail != null) {
            return true;
        }

        List<MiniAppDTO> mainApps = miniAppRemoteService.listMainMiniAppsFromCache(mid);
        Map<String, MiniAppDTO> mainAppMap = mainApps.stream()
                .collect(Collectors.toMap(MiniAppDTO::getAppId, Function.identity()));

        Integer permission = getPermission(appId, mainAppMap, null);
        return Objects.equals(permission, MiniAppPermission.SUPER_ADMIN.getCode());
    }

    @Override
    public boolean isAdmin(Long mid, String appId) {

        CompanyDetailBo companyDetail = companyService.getCreatedCompanyDetail(mid);

        return isAdmin(mid, appId, companyDetail);
    }

    @Override
    public Integer getPermission(String appId, Map<String, MiniAppDTO> mainAppMap, Map<String, MiniAppMemberDTO> otherAppMap) {
        if (!CollectionUtils.isEmpty(mainAppMap)) {
            if (mainAppMap.containsKey(appId)) {
                return MiniAppPermission.SUPER_ADMIN.getCode();
            }
        }

        if (!CollectionUtils.isEmpty(otherAppMap)) {
            MiniAppMemberDTO miniAppMemberDTO = otherAppMap.get(appId);
            if (miniAppMemberDTO == null) {
                return null;
            }
            byte hasPermission = 1;
            if (miniAppMemberDTO.getDeveloper() == hasPermission) {
                return MiniAppPermission.DEVELOPER.getCode();
            }
            if (miniAppMemberDTO.getPreview() == hasPermission) {
                return MiniAppPermission.EXPERIENCE.getCode();
            }
            if (miniAppMemberDTO.getOperation() == hasPermission) {
                return MiniAppPermission.OPERATION.getCode();
            }
        }
        return null;
    }
}
