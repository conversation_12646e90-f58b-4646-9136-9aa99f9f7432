package com.bilibili.miniapp.open.service.bo.income;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收入汇总响应BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeSummaryBo {
    
    /**
     * 可提现金额（分）
     */
    private BigDecimal withdrawableAmount;
    
    /**
     * 提现中金额（分）
     */
    private BigDecimal withdrawingAmount;
}
