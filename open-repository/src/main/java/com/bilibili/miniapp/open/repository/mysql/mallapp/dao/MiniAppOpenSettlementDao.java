package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenSettlementDao {
    long countByExample(MiniAppOpenSettlementPoExample example);

    int deleteByExample(MiniAppOpenSettlementPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenSettlementPo record);

    int insertBatch(List<MiniAppOpenSettlementPo> records);

    int insertUpdateBatch(List<MiniAppOpenSettlementPo> records);

    int insert(MiniAppOpenSettlementPo record);

    int insertUpdateSelective(MiniAppOpenSettlementPo record);

    int insertSelective(MiniAppOpenSettlementPo record);

    List<MiniAppOpenSettlementPo> selectByExample(MiniAppOpenSettlementPoExample example);

    MiniAppOpenSettlementPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenSettlementPo record, @Param("example") MiniAppOpenSettlementPoExample example);

    int updateByExample(@Param("record") MiniAppOpenSettlementPo record, @Param("example") MiniAppOpenSettlementPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenSettlementPo record);

    int updateByPrimaryKey(MiniAppOpenSettlementPo record);
}