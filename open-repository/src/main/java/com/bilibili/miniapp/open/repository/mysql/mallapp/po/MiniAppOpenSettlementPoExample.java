package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenSettlementPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenSettlementPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeIsNull() {
            addCriterion("settlement_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeIsNotNull() {
            addCriterion("settlement_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeEqualTo(Timestamp value) {
            addCriterion("settlement_begin_time =", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeNotEqualTo(Timestamp value) {
            addCriterion("settlement_begin_time <>", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeGreaterThan(Timestamp value) {
            addCriterion("settlement_begin_time >", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("settlement_begin_time >=", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeLessThan(Timestamp value) {
            addCriterion("settlement_begin_time <", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("settlement_begin_time <=", value, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeIn(List<Timestamp> values) {
            addCriterion("settlement_begin_time in", values, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeNotIn(List<Timestamp> values) {
            addCriterion("settlement_begin_time not in", values, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("settlement_begin_time between", value1, value2, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("settlement_begin_time not between", value1, value2, "settlementBeginTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeIsNull() {
            addCriterion("settlement_end_time is null");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeIsNotNull() {
            addCriterion("settlement_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeEqualTo(Timestamp value) {
            addCriterion("settlement_end_time =", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeNotEqualTo(Timestamp value) {
            addCriterion("settlement_end_time <>", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeGreaterThan(Timestamp value) {
            addCriterion("settlement_end_time >", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("settlement_end_time >=", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeLessThan(Timestamp value) {
            addCriterion("settlement_end_time <", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("settlement_end_time <=", value, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeIn(List<Timestamp> values) {
            addCriterion("settlement_end_time in", values, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeNotIn(List<Timestamp> values) {
            addCriterion("settlement_end_time not in", values, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("settlement_end_time between", value1, value2, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("settlement_end_time not between", value1, value2, "settlementEndTime");
            return (Criteria) this;
        }

        public Criteria andSettlementIdIsNull() {
            addCriterion("settlement_id is null");
            return (Criteria) this;
        }

        public Criteria andSettlementIdIsNotNull() {
            addCriterion("settlement_id is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementIdEqualTo(String value) {
            addCriterion("settlement_id =", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdNotEqualTo(String value) {
            addCriterion("settlement_id <>", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdGreaterThan(String value) {
            addCriterion("settlement_id >", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdGreaterThanOrEqualTo(String value) {
            addCriterion("settlement_id >=", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdLessThan(String value) {
            addCriterion("settlement_id <", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdLessThanOrEqualTo(String value) {
            addCriterion("settlement_id <=", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdLike(String value) {
            addCriterion("settlement_id like", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdNotLike(String value) {
            addCriterion("settlement_id not like", value, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdIn(List<String> values) {
            addCriterion("settlement_id in", values, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdNotIn(List<String> values) {
            addCriterion("settlement_id not in", values, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdBetween(String value1, String value2) {
            addCriterion("settlement_id between", value1, value2, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementIdNotBetween(String value1, String value2) {
            addCriterion("settlement_id not between", value1, value2, "settlementId");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusIsNull() {
            addCriterion("settlement_status is null");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusIsNotNull() {
            addCriterion("settlement_status is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusEqualTo(Integer value) {
            addCriterion("settlement_status =", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusNotEqualTo(Integer value) {
            addCriterion("settlement_status <>", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusGreaterThan(Integer value) {
            addCriterion("settlement_status >", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("settlement_status >=", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusLessThan(Integer value) {
            addCriterion("settlement_status <", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusLessThanOrEqualTo(Integer value) {
            addCriterion("settlement_status <=", value, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusIn(List<Integer> values) {
            addCriterion("settlement_status in", values, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusNotIn(List<Integer> values) {
            addCriterion("settlement_status not in", values, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusBetween(Integer value1, Integer value2) {
            addCriterion("settlement_status between", value1, value2, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andSettlementStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("settlement_status not between", value1, value2, "settlementStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdIsNull() {
            addCriterion("payment_order_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdIsNotNull() {
            addCriterion("payment_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdEqualTo(String value) {
            addCriterion("payment_order_id =", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdNotEqualTo(String value) {
            addCriterion("payment_order_id <>", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdGreaterThan(String value) {
            addCriterion("payment_order_id >", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("payment_order_id >=", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdLessThan(String value) {
            addCriterion("payment_order_id <", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdLessThanOrEqualTo(String value) {
            addCriterion("payment_order_id <=", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdLike(String value) {
            addCriterion("payment_order_id like", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdNotLike(String value) {
            addCriterion("payment_order_id not like", value, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdIn(List<String> values) {
            addCriterion("payment_order_id in", values, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdNotIn(List<String> values) {
            addCriterion("payment_order_id not in", values, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdBetween(String value1, String value2) {
            addCriterion("payment_order_id between", value1, value2, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andPaymentOrderIdNotBetween(String value1, String value2) {
            addCriterion("payment_order_id not between", value1, value2, "paymentOrderId");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountIsNull() {
            addCriterion("withdraw_apply_amount is null");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountIsNotNull() {
            addCriterion("withdraw_apply_amount is not null");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountEqualTo(BigDecimal value) {
            addCriterion("withdraw_apply_amount =", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountNotEqualTo(BigDecimal value) {
            addCriterion("withdraw_apply_amount <>", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountGreaterThan(BigDecimal value) {
            addCriterion("withdraw_apply_amount >", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("withdraw_apply_amount >=", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountLessThan(BigDecimal value) {
            addCriterion("withdraw_apply_amount <", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("withdraw_apply_amount <=", value, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountIn(List<BigDecimal> values) {
            addCriterion("withdraw_apply_amount in", values, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountNotIn(List<BigDecimal> values) {
            addCriterion("withdraw_apply_amount not in", values, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("withdraw_apply_amount between", value1, value2, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andWithdrawApplyAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("withdraw_apply_amount not between", value1, value2, "withdrawApplyAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountIsNull() {
            addCriterion("actual_withdraw_amount is null");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountIsNotNull() {
            addCriterion("actual_withdraw_amount is not null");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountEqualTo(BigDecimal value) {
            addCriterion("actual_withdraw_amount =", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountNotEqualTo(BigDecimal value) {
            addCriterion("actual_withdraw_amount <>", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountGreaterThan(BigDecimal value) {
            addCriterion("actual_withdraw_amount >", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_withdraw_amount >=", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountLessThan(BigDecimal value) {
            addCriterion("actual_withdraw_amount <", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_withdraw_amount <=", value, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountIn(List<BigDecimal> values) {
            addCriterion("actual_withdraw_amount in", values, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountNotIn(List<BigDecimal> values) {
            addCriterion("actual_withdraw_amount not in", values, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_withdraw_amount between", value1, value2, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andActualWithdrawAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_withdraw_amount not between", value1, value2, "actualWithdrawAmount");
            return (Criteria) this;
        }

        public Criteria andTaxFeeIsNull() {
            addCriterion("tax_fee is null");
            return (Criteria) this;
        }

        public Criteria andTaxFeeIsNotNull() {
            addCriterion("tax_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTaxFeeEqualTo(BigDecimal value) {
            addCriterion("tax_fee =", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeNotEqualTo(BigDecimal value) {
            addCriterion("tax_fee <>", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeGreaterThan(BigDecimal value) {
            addCriterion("tax_fee >", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_fee >=", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeLessThan(BigDecimal value) {
            addCriterion("tax_fee <", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_fee <=", value, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeIn(List<BigDecimal> values) {
            addCriterion("tax_fee in", values, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeNotIn(List<BigDecimal> values) {
            addCriterion("tax_fee not in", values, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_fee between", value1, value2, "taxFee");
            return (Criteria) this;
        }

        public Criteria andTaxFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_fee not between", value1, value2, "taxFee");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidIsNull() {
            addCriterion("invoice_oid is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidIsNotNull() {
            addCriterion("invoice_oid is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidEqualTo(String value) {
            addCriterion("invoice_oid =", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidNotEqualTo(String value) {
            addCriterion("invoice_oid <>", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidGreaterThan(String value) {
            addCriterion("invoice_oid >", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_oid >=", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidLessThan(String value) {
            addCriterion("invoice_oid <", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidLessThanOrEqualTo(String value) {
            addCriterion("invoice_oid <=", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidLike(String value) {
            addCriterion("invoice_oid like", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidNotLike(String value) {
            addCriterion("invoice_oid not like", value, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidIn(List<String> values) {
            addCriterion("invoice_oid in", values, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidNotIn(List<String> values) {
            addCriterion("invoice_oid not in", values, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidBetween(String value1, String value2) {
            addCriterion("invoice_oid between", value1, value2, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceOidNotBetween(String value1, String value2) {
            addCriterion("invoice_oid not between", value1, value2, "invoiceOid");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlIsNull() {
            addCriterion("invoice_url is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlIsNotNull() {
            addCriterion("invoice_url is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlEqualTo(String value) {
            addCriterion("invoice_url =", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlNotEqualTo(String value) {
            addCriterion("invoice_url <>", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlGreaterThan(String value) {
            addCriterion("invoice_url >", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_url >=", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlLessThan(String value) {
            addCriterion("invoice_url <", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlLessThanOrEqualTo(String value) {
            addCriterion("invoice_url <=", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlLike(String value) {
            addCriterion("invoice_url like", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlNotLike(String value) {
            addCriterion("invoice_url not like", value, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlIn(List<String> values) {
            addCriterion("invoice_url in", values, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlNotIn(List<String> values) {
            addCriterion("invoice_url not in", values, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlBetween(String value1, String value2) {
            addCriterion("invoice_url between", value1, value2, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceUrlNotBetween(String value1, String value2) {
            addCriterion("invoice_url not between", value1, value2, "invoiceUrl");
            return (Criteria) this;
        }

        public Criteria andExtraIsNull() {
            addCriterion("extra is null");
            return (Criteria) this;
        }

        public Criteria andExtraIsNotNull() {
            addCriterion("extra is not null");
            return (Criteria) this;
        }

        public Criteria andExtraEqualTo(String value) {
            addCriterion("extra =", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotEqualTo(String value) {
            addCriterion("extra <>", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThan(String value) {
            addCriterion("extra >", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraGreaterThanOrEqualTo(String value) {
            addCriterion("extra >=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThan(String value) {
            addCriterion("extra <", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLessThanOrEqualTo(String value) {
            addCriterion("extra <=", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraLike(String value) {
            addCriterion("extra like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotLike(String value) {
            addCriterion("extra not like", value, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraIn(List<String> values) {
            addCriterion("extra in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotIn(List<String> values) {
            addCriterion("extra not in", values, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraBetween(String value1, String value2) {
            addCriterion("extra between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andExtraNotBetween(String value1, String value2) {
            addCriterion("extra not between", value1, value2, "extra");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}