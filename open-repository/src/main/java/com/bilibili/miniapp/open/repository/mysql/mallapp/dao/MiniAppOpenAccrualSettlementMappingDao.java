package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualSettlementMappingPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualSettlementMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAccrualSettlementMappingDao {
    long countByExample(MiniAppOpenAccrualSettlementMappingPoExample example);

    int deleteByExample(MiniAppOpenAccrualSettlementMappingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAccrualSettlementMappingPo record);

    int insertBatch(List<MiniAppOpenAccrualSettlementMappingPo> records);

    int insertUpdateBatch(List<MiniAppOpenAccrualSettlementMappingPo> records);

    int insert(MiniAppOpenAccrualSettlementMappingPo record);

    int insertUpdateSelective(MiniAppOpenAccrualSettlementMappingPo record);

    int insertSelective(MiniAppOpenAccrualSettlementMappingPo record);

    List<MiniAppOpenAccrualSettlementMappingPo> selectByExample(MiniAppOpenAccrualSettlementMappingPoExample example);

    MiniAppOpenAccrualSettlementMappingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAccrualSettlementMappingPo record, @Param("example") MiniAppOpenAccrualSettlementMappingPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAccrualSettlementMappingPo record, @Param("example") MiniAppOpenAccrualSettlementMappingPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAccrualSettlementMappingPo record);

    int updateByPrimaryKey(MiniAppOpenAccrualSettlementMappingPo record);
}