package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenSettlementPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 结算开始时间
     */
    private Timestamp settlementBeginTime;

    /**
     * 结算结束时间
     */
    private Timestamp settlementEndTime;

    /**
     * 结算单id
     */
    private String settlementId;

    /**
     * 结算单状态：0-待确认，1-待上传发票，2-审核中，3-代付款，4-付款成功，-1-审核驳回，-2付款失败，-3-已作废
     */
    private Integer settlementStatus;

    /**
     * 汇联易付款单id
     */
    private String paymentOrderId;

    /**
     * 申请提现金额
     */
    private BigDecimal withdrawApplyAmount;

    /**
     * 实际提现金额，申请提现金额/（1+6%）*（1+适用税率）
     */
    private BigDecimal actualWithdrawAmount;

    /**
     * 税费：申请提现金额/（1+6%）*适用税率，进行四舍五入
     */
    private BigDecimal taxFee;

    /**
     * 发票图片上传汇联易后oid
     */
    private String invoiceOid;

    /**
     * 发票图片地址
     */
    private String invoiceUrl;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除，0-否，1-已删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}