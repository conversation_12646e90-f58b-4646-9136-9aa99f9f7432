package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenAccrualPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 收入日期 20250101，对应明细表日期
     */
    private String incomeDate;

    /**
     * 汇联易预提单id
     */
    private String accrualId;

    /**
     * 提现状态，由结算单的结算状态决定：0-提现冻结，1-可提现，2-提现中，3-提现成功
     */
    private Integer withdrawStatus;

    /**
     * 总金额，单位（分）
     */
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除，0-否，1-已删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}