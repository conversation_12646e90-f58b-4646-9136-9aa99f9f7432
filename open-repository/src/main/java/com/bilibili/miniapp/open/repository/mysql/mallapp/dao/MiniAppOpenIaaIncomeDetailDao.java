package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIaaIncomeDetailDao {
    long countByExample(MiniAppOpenIaaIncomeDetailPoExample example);

    int deleteByExample(MiniAppOpenIaaIncomeDetailPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIaaIncomeDetailPo record);

    int insertBatch(List<MiniAppOpenIaaIncomeDetailPo> records);

    int insertUpdateBatch(List<MiniAppOpenIaaIncomeDetailPo> records);

    int insert(MiniAppOpenIaaIncomeDetailPo record);

    int insertUpdateSelective(MiniAppOpenIaaIncomeDetailPo record);

    int insertSelective(MiniAppOpenIaaIncomeDetailPo record);

    List<MiniAppOpenIaaIncomeDetailPo> selectByExample(MiniAppOpenIaaIncomeDetailPoExample example);

    MiniAppOpenIaaIncomeDetailPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIaaIncomeDetailPo record, @Param("example") MiniAppOpenIaaIncomeDetailPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIaaIncomeDetailPo record, @Param("example") MiniAppOpenIaaIncomeDetailPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIaaIncomeDetailPo record);

    int updateByPrimaryKey(MiniAppOpenIaaIncomeDetailPo record);
}