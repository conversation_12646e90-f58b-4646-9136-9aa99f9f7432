package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAccrualDao {
    long countByExample(MiniAppOpenAccrualPoExample example);

    int deleteByExample(MiniAppOpenAccrualPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAccrualPo record);

    int insertBatch(List<MiniAppOpenAccrualPo> records);

    int insertUpdateBatch(List<MiniAppOpenAccrualPo> records);

    int insert(MiniAppOpenAccrualPo record);

    int insertUpdateSelective(MiniAppOpenAccrualPo record);

    int insertSelective(MiniAppOpenAccrualPo record);

    List<MiniAppOpenAccrualPo> selectByExample(MiniAppOpenAccrualPoExample example);

    MiniAppOpenAccrualPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAccrualPo record, @Param("example") MiniAppOpenAccrualPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAccrualPo record, @Param("example") MiniAppOpenAccrualPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAccrualPo record);

    int updateByPrimaryKey(MiniAppOpenAccrualPo record);
}