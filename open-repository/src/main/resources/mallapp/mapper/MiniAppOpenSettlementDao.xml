<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSettlementDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="settlement_begin_time" jdbcType="TIMESTAMP" property="settlementBeginTime" />
    <result column="settlement_end_time" jdbcType="TIMESTAMP" property="settlementEndTime" />
    <result column="settlement_id" jdbcType="VARCHAR" property="settlementId" />
    <result column="settlement_status" jdbcType="TINYINT" property="settlementStatus" />
    <result column="payment_order_id" jdbcType="VARCHAR" property="paymentOrderId" />
    <result column="withdraw_apply_amount" jdbcType="DECIMAL" property="withdrawApplyAmount" />
    <result column="actual_withdraw_amount" jdbcType="DECIMAL" property="actualWithdrawAmount" />
    <result column="tax_fee" jdbcType="DECIMAL" property="taxFee" />
    <result column="invoice_oid" jdbcType="VARCHAR" property="invoiceOid" />
    <result column="invoice_url" jdbcType="VARCHAR" property="invoiceUrl" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, settlement_begin_time, settlement_end_time, settlement_id, settlement_status, 
    payment_order_id, withdraw_apply_amount, actual_withdraw_amount, tax_fee, invoice_oid, 
    invoice_url, extra, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_settlement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample">
    delete from mini_app_open_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_settlement (app_id, settlement_begin_time, settlement_end_time, 
      settlement_id, settlement_status, payment_order_id, 
      withdraw_apply_amount, actual_withdraw_amount, 
      tax_fee, invoice_oid, invoice_url, 
      extra, ctime, mtime, 
      is_deleted)
    values (#{appId,jdbcType=VARCHAR}, #{settlementBeginTime,jdbcType=TIMESTAMP}, #{settlementEndTime,jdbcType=TIMESTAMP}, 
      #{settlementId,jdbcType=VARCHAR}, #{settlementStatus,jdbcType=TINYINT}, #{paymentOrderId,jdbcType=VARCHAR}, 
      #{withdrawApplyAmount,jdbcType=DECIMAL}, #{actualWithdrawAmount,jdbcType=DECIMAL}, 
      #{taxFee,jdbcType=DECIMAL}, #{invoiceOid,jdbcType=VARCHAR}, #{invoiceUrl,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="settlementBeginTime != null">
        settlement_begin_time,
      </if>
      <if test="settlementEndTime != null">
        settlement_end_time,
      </if>
      <if test="settlementId != null">
        settlement_id,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="paymentOrderId != null">
        payment_order_id,
      </if>
      <if test="withdrawApplyAmount != null">
        withdraw_apply_amount,
      </if>
      <if test="actualWithdrawAmount != null">
        actual_withdraw_amount,
      </if>
      <if test="taxFee != null">
        tax_fee,
      </if>
      <if test="invoiceOid != null">
        invoice_oid,
      </if>
      <if test="invoiceUrl != null">
        invoice_url,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="settlementBeginTime != null">
        #{settlementBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementEndTime != null">
        #{settlementEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementId != null">
        #{settlementId,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentOrderId != null">
        #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawApplyAmount != null">
        #{withdrawApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualWithdrawAmount != null">
        #{actualWithdrawAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxFee != null">
        #{taxFee,jdbcType=DECIMAL},
      </if>
      <if test="invoiceOid != null">
        #{invoiceOid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUrl != null">
        #{invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementBeginTime != null">
        settlement_begin_time = #{record.settlementBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementEndTime != null">
        settlement_end_time = #{record.settlementEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementId != null">
        settlement_id = #{record.settlementId,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementStatus != null">
        settlement_status = #{record.settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="record.paymentOrderId != null">
        payment_order_id = #{record.paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.withdrawApplyAmount != null">
        withdraw_apply_amount = #{record.withdrawApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.actualWithdrawAmount != null">
        actual_withdraw_amount = #{record.actualWithdrawAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxFee != null">
        tax_fee = #{record.taxFee,jdbcType=DECIMAL},
      </if>
      <if test="record.invoiceOid != null">
        invoice_oid = #{record.invoiceOid,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceUrl != null">
        invoice_url = #{record.invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_settlement
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      settlement_begin_time = #{record.settlementBeginTime,jdbcType=TIMESTAMP},
      settlement_end_time = #{record.settlementEndTime,jdbcType=TIMESTAMP},
      settlement_id = #{record.settlementId,jdbcType=VARCHAR},
      settlement_status = #{record.settlementStatus,jdbcType=TINYINT},
      payment_order_id = #{record.paymentOrderId,jdbcType=VARCHAR},
      withdraw_apply_amount = #{record.withdrawApplyAmount,jdbcType=DECIMAL},
      actual_withdraw_amount = #{record.actualWithdrawAmount,jdbcType=DECIMAL},
      tax_fee = #{record.taxFee,jdbcType=DECIMAL},
      invoice_oid = #{record.invoiceOid,jdbcType=VARCHAR},
      invoice_url = #{record.invoiceUrl,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    update mini_app_open_settlement
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="settlementBeginTime != null">
        settlement_begin_time = #{settlementBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementEndTime != null">
        settlement_end_time = #{settlementEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementId != null">
        settlement_id = #{settlementId,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentOrderId != null">
        payment_order_id = #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawApplyAmount != null">
        withdraw_apply_amount = #{withdrawApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualWithdrawAmount != null">
        actual_withdraw_amount = #{actualWithdrawAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxFee != null">
        tax_fee = #{taxFee,jdbcType=DECIMAL},
      </if>
      <if test="invoiceOid != null">
        invoice_oid = #{invoiceOid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUrl != null">
        invoice_url = #{invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    update mini_app_open_settlement
    set app_id = #{appId,jdbcType=VARCHAR},
      settlement_begin_time = #{settlementBeginTime,jdbcType=TIMESTAMP},
      settlement_end_time = #{settlementEndTime,jdbcType=TIMESTAMP},
      settlement_id = #{settlementId,jdbcType=VARCHAR},
      settlement_status = #{settlementStatus,jdbcType=TINYINT},
      payment_order_id = #{paymentOrderId,jdbcType=VARCHAR},
      withdraw_apply_amount = #{withdrawApplyAmount,jdbcType=DECIMAL},
      actual_withdraw_amount = #{actualWithdrawAmount,jdbcType=DECIMAL},
      tax_fee = #{taxFee,jdbcType=DECIMAL},
      invoice_oid = #{invoiceOid,jdbcType=VARCHAR},
      invoice_url = #{invoiceUrl,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_settlement (app_id, settlement_begin_time, settlement_end_time, 
      settlement_id, settlement_status, payment_order_id, 
      withdraw_apply_amount, actual_withdraw_amount, 
      tax_fee, invoice_oid, invoice_url, 
      extra, ctime, mtime, 
      is_deleted)
    values (#{appId,jdbcType=VARCHAR}, #{settlementBeginTime,jdbcType=TIMESTAMP}, #{settlementEndTime,jdbcType=TIMESTAMP}, 
      #{settlementId,jdbcType=VARCHAR}, #{settlementStatus,jdbcType=TINYINT}, #{paymentOrderId,jdbcType=VARCHAR}, 
      #{withdrawApplyAmount,jdbcType=DECIMAL}, #{actualWithdrawAmount,jdbcType=DECIMAL}, 
      #{taxFee,jdbcType=DECIMAL}, #{invoiceOid,jdbcType=VARCHAR}, #{invoiceUrl,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      settlement_begin_time = values(settlement_begin_time),
      settlement_end_time = values(settlement_end_time),
      settlement_id = values(settlement_id),
      settlement_status = values(settlement_status),
      payment_order_id = values(payment_order_id),
      withdraw_apply_amount = values(withdraw_apply_amount),
      actual_withdraw_amount = values(actual_withdraw_amount),
      tax_fee = values(tax_fee),
      invoice_oid = values(invoice_oid),
      invoice_url = values(invoice_url),
      extra = values(extra),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_settlement
      (app_id,settlement_begin_time,settlement_end_time,settlement_id,settlement_status,payment_order_id,withdraw_apply_amount,actual_withdraw_amount,tax_fee,invoice_oid,invoice_url,extra,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.settlementBeginTime,jdbcType=TIMESTAMP},
        #{item.settlementEndTime,jdbcType=TIMESTAMP},
        #{item.settlementId,jdbcType=VARCHAR},
        #{item.settlementStatus,jdbcType=TINYINT},
        #{item.paymentOrderId,jdbcType=VARCHAR},
        #{item.withdrawApplyAmount,jdbcType=DECIMAL},
        #{item.actualWithdrawAmount,jdbcType=DECIMAL},
        #{item.taxFee,jdbcType=DECIMAL},
        #{item.invoiceOid,jdbcType=VARCHAR},
        #{item.invoiceUrl,jdbcType=VARCHAR},
        #{item.extra,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_settlement
      (app_id,settlement_begin_time,settlement_end_time,settlement_id,settlement_status,payment_order_id,withdraw_apply_amount,actual_withdraw_amount,tax_fee,invoice_oid,invoice_url,extra,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.settlementBeginTime,jdbcType=TIMESTAMP},
        #{item.settlementEndTime,jdbcType=TIMESTAMP},
        #{item.settlementId,jdbcType=VARCHAR},
        #{item.settlementStatus,jdbcType=TINYINT},
        #{item.paymentOrderId,jdbcType=VARCHAR},
        #{item.withdrawApplyAmount,jdbcType=DECIMAL},
        #{item.actualWithdrawAmount,jdbcType=DECIMAL},
        #{item.taxFee,jdbcType=DECIMAL},
        #{item.invoiceOid,jdbcType=VARCHAR},
        #{item.invoiceUrl,jdbcType=VARCHAR},
        #{item.extra,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      settlement_begin_time = values(settlement_begin_time),
      settlement_end_time = values(settlement_end_time),
      settlement_id = values(settlement_id),
      settlement_status = values(settlement_status),
      payment_order_id = values(payment_order_id),
      withdraw_apply_amount = values(withdraw_apply_amount),
      actual_withdraw_amount = values(actual_withdraw_amount),
      tax_fee = values(tax_fee),
      invoice_oid = values(invoice_oid),
      invoice_url = values(invoice_url),
      extra = values(extra),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="settlementBeginTime != null">
        settlement_begin_time,
      </if>
      <if test="settlementEndTime != null">
        settlement_end_time,
      </if>
      <if test="settlementId != null">
        settlement_id,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="paymentOrderId != null">
        payment_order_id,
      </if>
      <if test="withdrawApplyAmount != null">
        withdraw_apply_amount,
      </if>
      <if test="actualWithdrawAmount != null">
        actual_withdraw_amount,
      </if>
      <if test="taxFee != null">
        tax_fee,
      </if>
      <if test="invoiceOid != null">
        invoice_oid,
      </if>
      <if test="invoiceUrl != null">
        invoice_url,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="settlementBeginTime != null">
        #{settlementBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementEndTime != null">
        #{settlementEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementId != null">
        #{settlementId,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentOrderId != null">
        #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="withdrawApplyAmount != null">
        #{withdrawApplyAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualWithdrawAmount != null">
        #{actualWithdrawAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxFee != null">
        #{taxFee,jdbcType=DECIMAL},
      </if>
      <if test="invoiceOid != null">
        #{invoiceOid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUrl != null">
        #{invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="settlementBeginTime != null">
        settlement_begin_time = values(settlement_begin_time),
      </if>
      <if test="settlementEndTime != null">
        settlement_end_time = values(settlement_end_time),
      </if>
      <if test="settlementId != null">
        settlement_id = values(settlement_id),
      </if>
      <if test="settlementStatus != null">
        settlement_status = values(settlement_status),
      </if>
      <if test="paymentOrderId != null">
        payment_order_id = values(payment_order_id),
      </if>
      <if test="withdrawApplyAmount != null">
        withdraw_apply_amount = values(withdraw_apply_amount),
      </if>
      <if test="actualWithdrawAmount != null">
        actual_withdraw_amount = values(actual_withdraw_amount),
      </if>
      <if test="taxFee != null">
        tax_fee = values(tax_fee),
      </if>
      <if test="invoiceOid != null">
        invoice_oid = values(invoice_oid),
      </if>
      <if test="invoiceUrl != null">
        invoice_url = values(invoice_url),
      </if>
      <if test="extra != null">
        extra = values(extra),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>