package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IncomeControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryRespVo;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 收入相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/income")
public class IncomeController extends AbstractController {

    @Autowired
    private IIncomeService incomeService;

    /**
     * 获取收入汇总
     */
    @GetMapping("/summary")
    @MainSiteLoginValidation
    public Response<IncomeSummaryRespVo> getIncomeSummary(Context context) {
        return Response.SUCCESS(IncomeControllerMapper.MAPPER.boToVo(
                incomeService.getIncomeSummary(context.getMid())));
    }
}
