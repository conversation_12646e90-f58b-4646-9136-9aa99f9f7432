package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryRespVo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryRespBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 收入控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface IncomeControllerMapper {

    IncomeControllerMapper MAPPER = Mappers.getMapper(IncomeControllerMapper.class);

    /**
     * 收入汇总BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawableAmount", expression = "java(convertCentToYuan(bo.getWithdrawableAmount()))")
    @Mapping(target = "withdrawingAmount", expression = "java(convertCentToYuan(bo.getWithdrawingAmount()))")
    IncomeSummaryRespVo boToVo(IncomeSummaryRespBo bo);

    /**
     * 分转元，保留2位小数
     */
    default String convertCentToYuan(BigDecimal cent) {
        if (cent == null) {
            return "0.00";
        }
        return cent.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString();
    }
}
