package com.bilibili.miniapp.open.portal.vo.income;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 收入汇总响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IncomeSummaryRespVo {
    
    /**
     * 可提现金额（元）
     */
    private String withdrawableAmount;
    
    /**
     * 提现中金额（元）
     */
    private String withdrawingAmount;
}
